"use client";
import React from 'react'
import Image from 'next/image'

interface ButtonProps {
  id: string;
}

export const Button = ({ id }: ButtonProps) => {
  return (
    <button 
      id={id} 
      className="group-relative flex items-center gap-2 bg-black-50 text-white px-8 py-3 rounded-full hover:bg-yellow-500 transition-opacity duration-300 ease-in-out relative z-20"
    >
      <span>Get Started</span>
      <Image 
        src="/img/play.svg" 
        alt="play"
        width={14}
        height={14}
        className="invert"
      />
    </button>
  )
}

export default Button