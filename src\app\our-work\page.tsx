"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// Sample project data
const projects = [
  {
    id: 1,
    title: 'Modern Website Design',
    category: 'Web Design',
    image: '/img/project1.jpg',
    description: 'A modern website design for a tech startup with focus on user experience and conversion optimization.',
    tags: ['React', 'Next.js', 'Tailwind CSS']
  },
  {
    id: 2,
    title: 'E-commerce Platform',
    category: 'Web Development',
    image: '/img/project2.jpg',
    description: 'Full-featured e-commerce platform with product management, cart functionality, and payment processing.',
    tags: ['Next.js', 'MongoDB', 'Stripe']
  },
  {
    id: 3,
    title: 'Mobile App UI Design',
    category: 'UI/UX Design',
    image: '/img/project3.jpg',
    description: 'Intuitive and engaging mobile app interface design for a fitness tracking application.',
    tags: ['Figma', 'Adobe XD', 'Prototyping']
  },
  {
    id: 4,
    title: 'Brand Identity Package',
    category: 'Branding',
    image: '/img/project4.jpg',
    description: 'Complete brand identity package including logo design, color palette, typography, and brand guidelines.',
    tags: ['Illustrator', 'Photoshop', 'Brand Strategy']
  },
  {
    id: 5,
    title: 'Corporate Website Redesign',
    category: 'Web Design',
    image: '/img/project5.jpg',
    description: 'Complete redesign of a corporate website to improve user experience and modernize the brand presence.',
    tags: ['React', 'Tailwind CSS', 'UX Research']
  },
  {
    id: 6,
    title: 'Social Media Dashboard',
    category: 'Web Development',
    image: '/img/project6.jpg',
    description: 'Analytics dashboard for tracking and managing social media performance across multiple platforms.',
    tags: ['React', 'Chart.js', 'API Integration']
  }
];

// Categories for filtering
const categories = ['All', 'Web Design', 'Web Development', 'UI/UX Design', 'Branding'];

export default function OurWorkPage() {
  const [activeCategory, setActiveCategory] = useState('All');
  const [selectedProject, setSelectedProject] = useState<null | typeof projects[0]>(null);

  // Filter projects based on active category
  const filteredProjects = activeCategory === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeCategory);

  return (
    <div className="min-h-screen bg-gray-900 text-white pb-20">
      {/* Hero Section */}
      <div className="w-full bg-black-50 py-20 px-6 md:px-12">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Work</h1>
          <p className="text-xl text-gray-300 max-w-2xl">
            Explore our portfolio of projects across web design, development, and branding.
            Each project represents our commitment to excellence and creativity.
          </p>
        </div>
      </div>

      {/* Category Filter */}
      <div className="max-w-6xl mx-auto px-6 md:px-12 py-10">
        <div className="flex flex-wrap gap-4 mb-12">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-2 rounded-full transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-yellow-500 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map(project => (
            <div 
              key={project.id} 
              className="bg-black-50 rounded-lg overflow-hidden transition-transform duration-300 hover:transform hover:scale-105 cursor-pointer"
              onClick={() => setSelectedProject(project)}
            >
              <div className="h-64 relative bg-gray-800">
                {/* Placeholder for project image */}
                <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                  <div className="text-4xl font-bold text-gray-700">{project.title.charAt(0)}</div>
                </div>
                {/* Uncomment when you have actual images */}
                {/* <Image 
                  src={project.image} 
                  alt={project.title}
                  fill
                  className="object-cover"
                /> */}
              </div>
              <div className="p-6">
                <div className="text-sm text-yellow-500 mb-2">{project.category}</div>
                <h3 className="text-xl font-bold mb-2">{project.title}</h3>
                <p className="text-gray-400 mb-4">{project.description}</p>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-800 text-xs px-3 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Project Modal */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50">
          <div className="bg-black-50 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">{selectedProject.title}</h2>
                <button 
                  onClick={() => setSelectedProject(null)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="h-80 relative bg-gray-800 mb-6 rounded-lg">
                {/* Placeholder for project image */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-6xl font-bold text-gray-700">{selectedProject.title.charAt(0)}</div>
                </div>
                {/* Uncomment when you have actual images */}
                {/* <Image 
                  src={selectedProject.image} 
                  alt={selectedProject.title}
                  fill
                  className="object-cover rounded-lg"
                /> */}
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-yellow-500 mb-2">{selectedProject.category}</div>
                <p className="text-gray-300 mb-4">{selectedProject.description}</p>
                <p className="text-gray-300 mb-4">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. 
                  Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus 
                  rhoncus ut eleifend nibh porttitor. Ut in nulla enim. Phasellus molestie magna 
                  non est bibendum non venenatis nisl tempor.
                </p>
              </div>
              
              <div className="flex flex-wrap gap-2 mb-6">
                {selectedProject.tags.map((tag, index) => (
                  <span key={index} className="bg-gray-800 text-xs px-3 py-1 rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="flex justify-end">
                <button 
                  onClick={() => setSelectedProject(null)}
                  className="bg-yellow-500 px-6 py-2 rounded-full hover:bg-yellow-600 transition-colors duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
