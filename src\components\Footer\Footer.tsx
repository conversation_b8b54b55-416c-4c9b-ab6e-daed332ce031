"use client";
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  return (
    <footer className="w-full bg-black-50 text-white py-12 px-3 md:px-12">
      <div className="max-w-full mx-auto grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 text-indigo-200">
        {/* Logo and Brand Column */}
        <div>
          <Link href="/" className="inline-block mb-6">
            <div className="w-12 h-12 bg-black rounded-md flex items-center justify-center">
              <svg viewBox="0 0 24 24" className="w-8 h-8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </Link>
        </div>

        {/* Explore Column */}
        <div>
          <h3 className="text-sm font-medium mb-4 uppercase tracking-wider">Explore</h3>
          <ul className="space-y-3">
            <li><Link href="/" className="hover:text-yellow-300 transition-colors">Home</Link></li>
            <li><Link href="/prologue" className="hover:text-yellow-300 transition-colors">Prologue</Link></li>
            <li><Link href="/about" className="hover:text-yellow-300 transition-colors">About</Link></li>
            <li><Link href="/contact" className="hover:text-yellow-300 transition-colors">Contact</Link></li>
          </ul>
        </div>

        {/* Products Column */}
        <div>
          <h3 className="text-sm font-medium mb-4 uppercase tracking-wider">Products</h3>
          <ul className="space-y-3">
            <li><Link href="/products/radiant" className="hover:text-yellow-300 transition-colors">Radiant</Link></li>
            <li>
              <Link href="/products/nexus" className="hover:text-yellow-300 transition-colors inline-flex items-center">
                Nexus
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </Link>
            </li>
            <li><Link href="/products/zigma" className="hover:text-yellow-300 transition-colors">Zigma</Link></li>
            <li><Link href="/products/azul" className="hover:text-yellow-300 transition-colors">Azul</Link></li>
          </ul>
        </div>

        {/* Follow Us Column */}
        <div>
          <h3 className="text-sm font-medium mb-4 uppercase tracking-wider">Follow Us</h3>
          <ul className="space-y-3">
            <li><Link href="https://discord.com" target="_blank" className="hover:text-yellow-300 transition-colors">Discord</Link></li>
            <li><Link href="https://x.com" target="_blank" className="hover:text-yellow-300 transition-colors">X</Link></li>
            <li><Link href="https://youtube.com" target="_blank" className="hover:text-yellow-300 transition-colors">Youtube</Link></li>
            <li><Link href="https://medium.com" target="_blank" className="hover:text-yellow-300 transition-colors">Medium</Link></li>
          </ul>
        </div>

        {/* Resources Column */}
        <div className="md:col-start-4 md:row-start-1">
          <h3 className="text-sm font-medium mb-4 uppercase tracking-wider">Resources</h3>
          <ul className="space-y-3">
            <li><Link href="/resources/media-kit" className="hover:text-yellow-300 transition-colors">Media Kit</Link></li>
          </ul>
        </div>
      </div>

      {/* Copyright Section */}
      <div className="max-w-full mx-auto mt-12 pt-8 border-t border-indigo-500">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-indigo-200">© {new Date().getFullYear()} PerPixel. All rights reserved.</p>
          <div className="mt-4 md:mt-0 flex space-x-6">
            <Link href="/privacy" className="text-sm text-indigo-200 hover:text-white">Privacy Policy</Link>
            <Link href="/terms" className="text-sm text-indigo-200 hover:text-white">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
