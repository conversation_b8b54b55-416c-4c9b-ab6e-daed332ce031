"use client";
import React, { useState } from 'react';
import Link from 'next/link';

interface NavbarProps {
  className?: string;
}

export const Navbar = ({ className = "" }: NavbarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={`w-full py-4 px-6 md:px-12 flex justify-between items-center bg-black-50 text-white ${className}`}>
      {/* Logo */}
      <div className="flex items-center">
        <Link href="/" className="text-xl font-bold">PerPixel</Link>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center gap-8">
        <Link href="/about" className="hover:text-yellow-500 transition-colors duration-300">About</Link>
        <Link href="/our-work" className="hover:text-yellow-500 transition-colors duration-300">Our Work</Link>
        <Link href="/contact" className="hover:text-yellow-500 transition-colors duration-300">Contact</Link>
        {/* Login and Signup, LATER TO BE ADDED IN PAGE ; NOT NEEDED NOW */}
        {/* <Link 
          href="/login" 
          className="bg-transparent border border-white px-6 py-2 rounded-full hover:bg-yellow-500 hover:border-yellow-500 transition-all duration-300"
        >
          Login
        </Link>
        <Link 
          href="/signup" 
          className="bg-yellow-500 border border-yellow-500 px-6 py-2 rounded-full hover:bg-yellow-600 hover:border-yellow-600 transition-all duration-300"
        >
          Sign Up
        </Link> */}
      </div>

      {/* Mobile Menu Button */}
      <div className="md:hidden">
        <button 
          onClick={toggleMenu}
          className="focus:outline-none"
          aria-label="Toggle menu"
        >
          {isMenuOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-16 left-0 right-0 bg-black-50 z-50 p-4 flex flex-col gap-4">
          <Link 
            href="/about" 
            className="block py-2 hover:text-yellow-500 transition-colors duration-300"
            onClick={() => setIsMenuOpen(false)}
          >
            About
          </Link>
          <Link 
            href="/our-work" 
            className="block py-2 hover:text-yellow-500 transition-colors duration-300"
            onClick={() => setIsMenuOpen(false)}
          >
            Our Work
          </Link>
          <Link 
            href="/contact" 
            className="block py-2 hover:text-yellow-500 transition-colors duration-300"
            onClick={() => setIsMenuOpen(false)}
          >
            Contact
          </Link>
          <Link 
            href="/login" 
            className="block border border-white px-6 py-2 rounded-full text-center hover:bg-yellow-500 hover:border-yellow-500 transition-all duration-300"
            onClick={() => setIsMenuOpen(false)}
          >
            Login
          </Link>
          <Link 
            href="/signup" 
            className="block bg-yellow-500 border border-yellow-500 px-6 py-2 rounded-full text-center hover:bg-yellow-600 hover:border-yellow-600 transition-all duration-300"
            onClick={() => setIsMenuOpen(false)}
          >
            Sign Up
          </Link>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
