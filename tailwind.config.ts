import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        circular: ['circular-web', 'sans-serif'],
        general: ['general', 'sans-serif'],
        robertMedium: ['robert-medium', 'sans-serif'],
        robertRegular: ['robert-regular', 'sans-serif'],
        zentryRegular: ['zentry-regular', 'sans-serif'],
        inter: ['Inter-VariableFont_opsz,wght', 'sans-serif'],
      },
      
      colors: {
        blue: {
          50: '#704fba',
          75: '#704da5',
          100: '#70488f',
          200: '#704679',
          300: '#704463',
          400: '#70424d',
          500: '#704037',
          600: '#703e21',
          700: '#703c0b'
        },
        black: {
          50: '#131d27',
        },
        yellow: {
          100: '#8E983F',
          300: '#EDFF66',
        },
        violet: {
          300: '#5724FF',
        },
      },
      textStrokeWidth: {
        '1': '1px',
        '2': '2px',
        '3': '3px',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'float-delayed': 'float 6s ease-in-out 2s infinite',
        'float-reverse': 'float 6s ease-in-out infinite reverse',
        'spin-slow': 'spin 8s linear infinite',
        'bounce-slow': 'bounce 3s infinite',
        'rotate': 'rotate 8s linear infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        rotate: {
          'from': { transform: 'rotate(0deg)' },
          'to': { transform: 'rotate(360deg)' }
        }
      },
    },
  },
  plugins: [],
};

export default config;
