"use client";
import React from 'react'
import Image from 'next/image'
import <PERSON><PERSON> from './Button'

const Hero = () => {
  return (
    <div className="bg-gradient-to-b from-[#1F2551] to-[#120B4F] h-[100vh] w-full">
      <section className='relative h-full w-full flex flex-col items-center pt-22'>
        <h1 className='text-[260px] font-black font-inter text-transparent [-webkit-text-stroke:1px_#ffffff]'>PER PIXEL</h1>

        {/* Shape 1 - Circle with rotation and float */}
        <div className='absolute top-[20%] left-[25%] w-16 h-16 animate-float'>
          <Image 
            src='/img/circle.svg'
            alt='floating circle'
            width={64}
            height={64}
            className='object-contain animate-spin-slow hover:scale-110 transition-transform'
          />
        </div>
        
        {/* Shape 2 - Cube with blur and scale */}
        <div className='absolute top-[-10%] right-[-10%] w-300 h-300 animate-float-delayed'>
          <Image 
            src='/img/cube.svg'
            alt='floating cube'
            width={300}
            height={300}
            className='object-contain blur-[1px] scale-105 hover:blur-none transition-all transform rotate-[80deg]'
          />
        </div>

      {/* Shape 3 - Cube with different animation */}
      <div className='absolute bottom-[25%] left-[35%] w-12 h-12 animate-float-reverse'>
          <Image 
            src='/img/cube.svg'
            alt='floating cube'
            width={48}
            height={48}
            className='object-contain hover:blur-none transition-all transform rotate-[-10deg] transform-gpu animate-bounce-slow'
          />
        </div>

         {/* Shape 4 with cube scale and on left - Changed z-index to go behind */}
         <div className='absolute bottom-[-0%] left-[-10%] w-300 h-300 animate-float-delayed z-0'>
          <Image 
            src='/img/cube.svg'
            alt='floating cube'
            width={300}
            height={300}
            className='object-contain blur-[1px] scale-100 hover:blur-none transition-all transform rotate-[180deg] transform-gpu'
          />
        </div>

        {/* Main image */}
        <div className='absolute top-[10%] w-[500px] h-[500px]'>
          <Image 
            src='/img/hero.svg' 
            alt='hero'
            width={500}
            height={500}
            className='object-contain'
            priority
          />
        </div>

        {/* Button */}
        <div className="absolute top-[20%] w-[500px] h-[500px] flex items-end justify-center pb-10">
          <Button id="getStarted"/>
        </div>
      </section>
    </div>
  )
}

export default Hero