"use client";
import React from 'react';
import Link from 'next/link';

export default function AboutPage() {
  // Team members data
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula.',
      image: '/img/team1.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Creative Director',
      bio: 'Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse dictum feugiat nisl ut dapibus.',
      image: '/img/team2.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Lead Developer',
      bio: 'Proin quis tortor orci. Etiam at risus et justo dignissim congue. Donec congue lacinia dui, a porttitor lectus.',
      image: '/img/team3.jpg'
    },
    {
      name: '<PERSON>',
      role: 'UI/UX Designer',
      bio: '<PERSON><PERSON><PERSON> iaculis porttitor posuere. Praesent id metus massa, ut blandit odio. Proin quis tortor orci.',
      image: '/img/team4.jpg'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white pb-20">
      {/* Hero Section */}
      <div className="w-full bg-black-50 py-20 px-6 md:px-12">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">About Us</h1>
          <p className="text-xl text-gray-300 max-w-2xl">
            We are a team of passionate designers and developers creating exceptional digital experiences.
          </p>
        </div>
      </div>

      {/* Our Story Section */}
      <section className="max-w-6xl mx-auto px-6 md:px-12 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-6">Our Story</h2>
            <div className="space-y-4">
              <p className="text-gray-300">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. 
                Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus 
                rhoncus ut eleifend nibh porttitor. Ut in nulla enim.
              </p>
              <p className="text-gray-300">
                Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse 
                dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus 
                massa, ut blandit odio.
              </p>
              <p className="text-gray-300">
                Proin quis tortor orci. Etiam at risus et justo dignissim congue. Donec congue 
                lacinia dui, a porttitor lectus condimentum laoreet. Nunc eu ullamcorper orci. 
                Quisque eget odio ac lectus vestibulum faucibus eget in metus.
              </p>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg overflow-hidden h-80 relative">
            {/* Placeholder for company image */}
            <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
              <div className="text-6xl font-bold text-gray-700">PerPixel</div>
            </div>
            {/* Uncomment when you have actual images */}
            {/* <Image 
              src="/img/about-company.jpg" 
              alt="Our company" 
              fill
              className="object-cover"
            /> */}
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="bg-black-50 py-16 px-6 md:px-12 my-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-gray-300">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. 
                Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus 
                rhoncus ut eleifend nibh porttitor. Ut in nulla enim. Phasellus molestie magna 
                non est bibendum non venenatis nisl tempor. Suspendisse dictum feugiat nisl ut dapibus.
              </p>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Vision</h2>
              <p className="text-gray-300">
                Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse 
                dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus 
                massa, ut blandit odio. Proin quis tortor orci. Etiam at risus et justo dignissim congue.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="max-w-6xl mx-auto px-6 md:px-12 py-16">
        <h2 className="text-3xl font-bold mb-12 text-center">Our Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-black-50 p-8 rounded-lg text-center">
            <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-4">Innovation</h3>
            <p className="text-gray-300">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. 
              Vivamus hendrerit arcu sed erat molestie vehicula.
            </p>
          </div>
          <div className="bg-black-50 p-8 rounded-lg text-center">
            <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-4">Collaboration</h3>
            <p className="text-gray-300">
              Phasellus molestie magna non est bibendum non venenatis nisl tempor. 
              Suspendisse dictum feugiat nisl ut dapibus.
            </p>
          </div>
          <div className="bg-black-50 p-8 rounded-lg text-center">
            <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-4">Quality</h3>
            <p className="text-gray-300">
              Mauris iaculis porttitor posuere. Praesent id metus massa, ut blandit odio. 
              Proin quis tortor orci. Etiam at risus.
            </p>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="max-w-6xl mx-auto px-6 md:px-12 py-16">
        <h2 className="text-3xl font-bold mb-12 text-center">Meet Our Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <div key={index} className="bg-black-50 rounded-lg overflow-hidden">
              <div className="h-64 relative bg-gray-800">
                {/* Placeholder for team member image */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-4xl font-bold text-gray-700">{member.name.charAt(0)}</div>
                </div>
                {/* Uncomment when you have actual images */}
                {/* <Image 
                  src={member.image} 
                  alt={member.name}
                  fill
                  className="object-cover"
                /> */}
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-1">{member.name}</h3>
                <p className="text-yellow-500 mb-4">{member.role}</p>
                <p className="text-gray-300">{member.bio}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-yellow-500 py-16 px-6 md:px-12">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-black">Ready to work with us?</h2>
          <p className="text-black mb-8 max-w-2xl mx-auto">
            Let's create something amazing together. Contact us today to discuss your project.
          </p>
          <Link 
            href="/contact" 
            className="inline-block bg-black px-8 py-3 rounded-full text-white font-medium hover:bg-gray-800 transition-colors duration-300"
          >
            Get in Touch
          </Link>
        </div>
      </section>
    </div>
  );
}
