@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'circular-web', sans-serif;
  width: 100%;
  background-color: #dfdff0;
  overflow-x: hidden;
}

@layer base, components, utilities {
  @font-face {
    font-family: 'Inter-VariableFont_opsz,wght';
    src: url('/fonts/Inter-VariableFont_opsz,wght.woff2') format('woff2-variations');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'circular-web';
    src: url('/fonts/circular-web-book.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'general';
    src: url('/fonts/general.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'robert-medium';
    src: url('/fonts/robert-medium.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'robert-regular';
    src: url('/fonts/robert-regular.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'zentry-regular';
    src: url('/fonts/zentry-regular.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
}

[src="/img/hero.svg"] {
  background: transparent !important;
}
