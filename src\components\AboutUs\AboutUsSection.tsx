"use client";
import React from 'react';
import Link from 'next/link';

export default function AboutUsSection() {
  return (
    <section className="w-full bg-gray-900 py-20 px-6 md:px-12">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">About Us</h2>
            <div className="space-y-4">
              <p className="text-gray-300">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. 
                Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus 
                rhoncus ut eleifend nibh porttitor. Ut in nulla enim.
              </p>
              <p className="text-gray-300">
                Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse 
                dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus 
                massa, ut blandit odio.
              </p>
              <p className="text-gray-300">
                Proin quis tortor orci. Etiam at risus et justo dignissim congue. Donec congue 
                lacinia dui, a porttitor lectus condimentum laoreet. Nunc eu ullamcorper orci. 
                Quisque eget odio ac lectus vestibulum faucibus eget in metus.
              </p>
            </div>
            <div className="mt-8">
              <Link 
                href="/about" 
                className="inline-block bg-transparent border border-white px-6 py-2 rounded-full hover:bg-yellow-500 hover:border-yellow-500 transition-all duration-300 text-white"
              >
                Learn More About Us
              </Link>
            </div>
          </div>
          
          {/* Right Column - Stats and Info */}
          <div className="bg-black-50 p-8 rounded-lg">
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center p-4">
                <div className="text-4xl font-bold text-yellow-500 mb-2">10+</div>
                <div className="text-gray-300">Years Experience</div>
              </div>
              <div className="text-center p-4">
                <div className="text-4xl font-bold text-yellow-500 mb-2">250+</div>
                <div className="text-gray-300">Projects Completed</div>
              </div>
              <div className="text-center p-4">
                <div className="text-4xl font-bold text-yellow-500 mb-2">15+</div>
                <div className="text-gray-300">Team Members</div>
              </div>
              <div className="text-center p-4">
                <div className="text-4xl font-bold text-yellow-500 mb-2">99%</div>
                <div className="text-gray-300">Client Satisfaction</div>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-gray-800 rounded-lg">
              <h3 className="text-xl font-bold text-white mb-3">Our Expertise</h3>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-300">Web Design</span>
                    <span className="text-gray-300">95%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-300">Web Development</span>
                    <span className="text-gray-300">90%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-300">UI/UX Design</span>
                    <span className="text-gray-300">85%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-300">Branding</span>
                    <span className="text-gray-300">80%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '80%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
