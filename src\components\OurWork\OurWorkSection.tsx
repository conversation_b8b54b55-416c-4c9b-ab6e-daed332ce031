"use client";
import React from 'react';
import Link from 'next/link';

// Using the same project data structure as in the main Our Work page
const featuredProjects = [
  {
    id: 1,
    title: 'Modern Website Design',
    category: 'Web Design',
    image: '/img/project1.jpg',
    description: 'A modern website design for a tech startup with focus on user experience and conversion optimization.',
    tags: ['React', 'Next.js', 'Tailwind CSS']
  },
  {
    id: 2,
    title: 'E-commerce Platform',
    category: 'Web Development',
    image: '/img/project2.jpg',
    description: 'Full-featured e-commerce platform with product management, cart functionality, and payment processing.',
    tags: ['Next.js', 'MongoDB', 'Stripe']
  },
  {
    id: 3,
    title: 'Mobile App UI Design',
    category: 'UI/UX Design',
    image: '/img/project3.jpg',
    description: 'Intuitive and engaging mobile app interface design for a fitness tracking application.',
    tags: ['Figma', 'Adobe XD', 'Prototyping']
  }
];

export default function OurWorkSection() {
  return (
    <section className="w-full bg-black-50 py-20 px-6 md:px-12">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-end mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Our Work</h2>
            <p className="text-gray-300 max-w-2xl">
              Explore some of our recent projects across web design, development, and branding.
              Each project represents our commitment to excellence and creativity.
            </p>
          </div>
          <Link 
            href="/our-work" 
            className="mt-6 md:mt-0 bg-transparent border border-white px-6 py-2 rounded-full hover:bg-yellow-500 hover:border-yellow-500 transition-all duration-300 text-white"
          >
            View All Projects
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredProjects.map(project => (
            <div 
              key={project.id} 
              className="bg-gray-900 rounded-lg overflow-hidden transition-transform duration-300 hover:transform hover:scale-105"
            >
              <div className="h-64 relative bg-gray-800">
                {/* Placeholder for project image */}
                <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                  <div className="text-4xl font-bold text-gray-700">{project.title.charAt(0)}</div>
                </div>
                {/* Uncomment when you have actual images */}
                {/* <Image 
                  src={project.image} 
                  alt={project.title}
                  fill
                  className="object-cover"
                /> */}
              </div>
              <div className="p-6">
                <div className="text-sm text-yellow-500 mb-2">{project.category}</div>
                <h3 className="text-xl font-bold mb-2 text-white">{project.title}</h3>
                <p className="text-gray-400 mb-4">{project.description}</p>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-800 text-xs px-3 py-1 rounded-full text-gray-300">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link 
            href="/our-work" 
            className="inline-block bg-yellow-500 border border-yellow-500 px-8 py-3 rounded-full hover:bg-yellow-600 hover:border-yellow-600 transition-all duration-300 text-white font-medium"
          >
            Show More Projects
          </Link>
        </div>
      </div>
    </section>
  );
}
